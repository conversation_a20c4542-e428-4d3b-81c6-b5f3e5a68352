cargo:rerun-if-changed=build.rs
cargo:rustc-cfg=static_assertions
cargo:rustc-cfg=linux_raw
cargo:rustc-cfg=linux_like
cargo:rustc-cfg=linux_kernel
cargo:rerun-if-env-changed=CARGO_CFG_RUSTIX_USE_EXPERIMENTAL_ASM
cargo:rerun-if-env-changed=CARGO_CFG_RUSTIX_USE_LIBC
cargo:rerun-if-env-changed=CARGO_FEATURE_USE_LIBC
cargo:rerun-if-env-changed=CARGO_FEATURE_RUSTC_DEP_OF_STD
cargo:rerun-if-env-changed=CARGO_CFG_MIRI
