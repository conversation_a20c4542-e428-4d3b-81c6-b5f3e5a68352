const FEATURES: &[(crate::Architecture, &str, &str, &[Feature])] = &[
    (crate::Architecture::Arm, "aclass", "Is application profile ('A' series).", &[]),
    (crate::Architecture::Arm, "aes", "Enable AES support.", &[Feature(3), Feature(9), Feature(21), Feature(22)]),
    (crate::Architecture::Arm, "crc", "Enable support for CRC instructions.", &[]),
    (crate::Architecture::Arm, "d32", "Extend FP to 32 double registers.", &[]),
    (crate::Architecture::Arm, "dotprod", "Enable support for dot product instructions.", &[Feature(3), Feature(9), Feature(21), Feature(22)]),
    (crate::Architecture::Arm, "dsp", "Supports DSP instructions in ARM and/or Thumb2.", &[]),
    (crate::Architecture::Arm, "fp-armv8", "Enable ARMv8 FP.", &[Feature(3), Feature(21), Feature(22), Feature(23)]),
    (crate::Architecture::Arm, "i8mm", "Enable Matrix Multiply Int8 Extension.", &[Feature(3), Feature(9), Feature(21), Feature(22)]),
    (crate::Architecture::Arm, "mclass", "Is microcontroller profile ('M' series).", &[]),
    (crate::Architecture::Arm, "neon", "Enable NEON instructions.", &[Feature(3), Feature(21), Feature(22)]),
    (crate::Architecture::Arm, "rclass", "Is realtime profile ('R' series).", &[]),
    (crate::Architecture::Arm, "sha2", "Enable SHA1 and SHA256 support.", &[Feature(3), Feature(9), Feature(21), Feature(22)]),
    (crate::Architecture::Arm, "thumb-mode", "Thumb mode.", &[]),
    (crate::Architecture::Arm, "thumb2", "Enable Thumb2 instructions.", &[]),
    (crate::Architecture::Arm, "trustzone", "Enable support for TrustZone security extensions.", &[]),
    (crate::Architecture::Arm, "v5te", "Support ARM v5TE, v5TEj, and v5TExp instructions.", &[]),
    (crate::Architecture::Arm, "v6", "Support ARM v6 instructions.", &[Feature(15)]),
    (crate::Architecture::Arm, "v6k", "Support ARM v6k instructions.", &[Feature(15), Feature(16)]),
    (crate::Architecture::Arm, "v6t2", "Support ARM v6t2 instructions.", &[Feature(13), Feature(15), Feature(16), Feature(17)]),
    (crate::Architecture::Arm, "v7", "Support ARM v7 instructions.", &[Feature(13), Feature(15), Feature(16), Feature(17), Feature(18)]),
    (crate::Architecture::Arm, "v8", "Support ARM v8 instructions.", &[Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19)]),
    (crate::Architecture::Arm, "vfp2", "Enable VFP2 instructions.", &[]),
    (crate::Architecture::Arm, "vfp3", "Enable VFP3 instructions.", &[Feature(3), Feature(21)]),
    (crate::Architecture::Arm, "vfp4", "Enable VFP4 instructions.", &[Feature(3), Feature(21), Feature(22)]),
    (crate::Architecture::Arm, "virtualization", "Supports Virtualization extension.", &[]),
    (crate::Architecture::Arm, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[]),
    (crate::Architecture::AArch64, "aes", "Enable AES support (FEAT_AES, FEAT_PMULL).", &[Feature(46)]),
    (crate::Architecture::AArch64, "bf16", "Enable BFloat16 Extension (FEAT_BF16).", &[]),
    (crate::Architecture::AArch64, "bti", "Enable Branch Target Identification (FEAT_BTI).", &[]),
    (crate::Architecture::AArch64, "crc", "Enable ARMv8 CRC-32 checksum instructions (FEAT_CRC32).", &[]),
    (crate::Architecture::AArch64, "dit", "Enable v8.4-A Data Independent Timing instructions (FEAT_DIT).", &[]),
    (crate::Architecture::AArch64, "dotprod", "Enable dot product support (FEAT_DotProd).", &[]),
    (crate::Architecture::AArch64, "dpb", "Enable v8.2 data Cache Clean to Point of Persistence (FEAT_DPB).", &[]),
    (crate::Architecture::AArch64, "dpb2", "Enable v8.5 Cache Clean to Point of Deep Persistence (FEAT_DPB2).", &[]),
    (crate::Architecture::AArch64, "f32mm", "Enable Matrix Multiply FP32 Extension (FEAT_F32MM).", &[Feature(39), Feature(46), Feature(62)]),
    (crate::Architecture::AArch64, "f64mm", "Enable Matrix Multiply FP64 Extension (FEAT_F64MM).", &[Feature(39), Feature(46), Feature(62)]),
    (crate::Architecture::AArch64, "fcma", "Enable v8.3-A Floating-point complex number support (FEAT_FCMA).", &[Feature(46)]),
    (crate::Architecture::AArch64, "fhm", "Enable FP16 FML instructions (FEAT_FHM).", &[Feature(39), Feature(46)]),
    (crate::Architecture::AArch64, "flagm", "Enable v8.4-A Flag Manipulation Instructions (FEAT_FlagM).", &[]),
    (crate::Architecture::AArch64, "fp16", "Full FP16 (FEAT_FP16).", &[Feature(46)]),
    (crate::Architecture::AArch64, "frintts", "Enable FRInt[32|64][Z|X] instructions that round a floating-point number to an integer (in FP format) forcing it to fit into a 32- or 64-bit int (FEAT_FRINTTS).", &[]),
    (crate::Architecture::AArch64, "i8mm", "Enable Matrix Multiply Int8 Extension (FEAT_I8MM).", &[]),
    (crate::Architecture::AArch64, "jsconv", "Enable v8.3-A JavaScript FP conversion instructions (FEAT_JSCVT).", &[Feature(46)]),
    (crate::Architecture::AArch64, "lor", "Enables ARM v8.1 Limited Ordering Regions extension (FEAT_LOR).", &[]),
    (crate::Architecture::AArch64, "lse", "Enable ARMv8.1 Large System Extension (LSE) atomic instructions (FEAT_LSE).", &[]),
    (crate::Architecture::AArch64, "mte", "Enable Memory Tagging Extension (FEAT_MTE, FEAT_MTE2).", &[]),
    (crate::Architecture::AArch64, "neon", "Enable Advanced SIMD instructions (FEAT_AdvSIMD).", &[]),
    (crate::Architecture::AArch64, "paca", "Enable v8.3-A Pointer Authentication extension (FEAT_PAuth).", &[Feature(48)]),
    (crate::Architecture::AArch64, "pacg", "Enable v8.3-A Pointer Authentication extension (FEAT_PAuth).", &[Feature(47)]),
    (crate::Architecture::AArch64, "pan", "Enables ARM v8.1 Privileged Access-Never extension (FEAT_PAN).", &[]),
    (crate::Architecture::AArch64, "pmuv3", "Enable Code Generation for ARMv8 PMUv3 Performance Monitors extension (FEAT_PMUv3).", &[]),
    (crate::Architecture::AArch64, "rand", "Enable Random Number generation instructions (FEAT_RNG).", &[]),
    (crate::Architecture::AArch64, "ras", "Enable ARMv8 Reliability, Availability and Serviceability Extensions (FEAT_RAS, FEAT_RASv1p1).", &[]),
    (crate::Architecture::AArch64, "rcpc", "Enable support for RCPC extension (FEAT_LRCPC).", &[]),
    (crate::Architecture::AArch64, "rcpc2", "Enable v8.4-A RCPC instructions with Immediate Offsets (FEAT_LRCPC2).", &[Feature(53)]),
    (crate::Architecture::AArch64, "rdm", "Enable ARMv8.1 Rounding Double Multiply Add/Subtract instructions (FEAT_RDM).", &[]),
    (crate::Architecture::AArch64, "sb", "Enable v8.5 Speculation Barrier (FEAT_SB).", &[]),
    (crate::Architecture::AArch64, "sha2", "Enable SHA1 and SHA256 support (FEAT_SHA1, FEAT_SHA256).", &[Feature(46)]),
    (crate::Architecture::AArch64, "sha3", "Enable SHA512 and SHA3 support (FEAT_SHA3, FEAT_SHA512).", &[Feature(46), Feature(57)]),
    (crate::Architecture::AArch64, "sm4", "Enable SM3 and SM4 support (FEAT_SM4, FEAT_SM3).", &[Feature(46)]),
    (crate::Architecture::AArch64, "spe", "Enable Statistical Profiling extension (FEAT_SPE).", &[]),
    (crate::Architecture::AArch64, "ssbs", "Enable Speculative Store Bypass Safe bit (FEAT_SSBS, FEAT_SSBS2).", &[]),
    (crate::Architecture::AArch64, "sve", "Enable Scalable Vector Extension (SVE) instructions (FEAT_SVE).", &[Feature(39), Feature(46)]),
    (crate::Architecture::AArch64, "sve2", "Enable Scalable Vector Extension 2 (SVE2) instructions (FEAT_SVE2).", &[Feature(39), Feature(46), Feature(62)]),
    (crate::Architecture::AArch64, "sve2-aes", "Enable AES SVE2 instructions (FEAT_SVE_AES, FEAT_SVE_PMULL128).", &[Feature(26), Feature(39), Feature(46), Feature(62), Feature(63)]),
    (crate::Architecture::AArch64, "sve2-bitperm", "Enable bit permutation SVE2 instructions (FEAT_SVE_BitPerm).", &[Feature(39), Feature(46), Feature(62), Feature(63)]),
    (crate::Architecture::AArch64, "sve2-sha3", "Enable SHA3 SVE2 instructions (FEAT_SVE_SHA3).", &[Feature(39), Feature(46), Feature(57), Feature(58), Feature(62), Feature(63)]),
    (crate::Architecture::AArch64, "sve2-sm4", "Enable SM4 SVE2 instructions (FEAT_SVE_SM4).", &[Feature(39), Feature(46), Feature(59), Feature(62), Feature(63)]),
    (crate::Architecture::AArch64, "tme", "Enable Transactional Memory Extension (FEAT_TME).", &[]),
    (crate::Architecture::AArch64, "v8.1a", "Support ARM v8.1a instructions.", &[Feature(29), Feature(43), Feature(44), Feature(49), Feature(55), Feature(76)]),
    (crate::Architecture::AArch64, "v8.2a", "Support ARM v8.2a instructions.", &[Feature(29), Feature(32), Feature(43), Feature(44), Feature(49), Feature(52), Feature(55), Feature(69), Feature(76)]),
    (crate::Architecture::AArch64, "v8.3a", "Support ARM v8.3a instructions.", &[Feature(29), Feature(32), Feature(36), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(52), Feature(53), Feature(55), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "v8.4a", "Support ARM v8.4a instructions.", &[Feature(29), Feature(30), Feature(31), Feature(32), Feature(36), Feature(38), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(52), Feature(53), Feature(54), Feature(55), Feature(69), Feature(70), Feature(71), Feature(76)]),
    (crate::Architecture::AArch64, "v8.5a", "Support ARM v8.5a instructions.", &[Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(38), Feature(40), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(76)]),
    (crate::Architecture::AArch64, "v8.6a", "Support ARM v8.6a instructions.", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(38), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(76)]),
    (crate::Architecture::AArch64, "v8.7a", "Support ARM v8.7a instructions.", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(38), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "vh", "Enables ARM v8.1 Virtual Host extension (FEAT_VHE).", &[]),
    (crate::Architecture::AArch64, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[]),
    (crate::Architecture::Bpf, "alu32", "Enable ALU32 instructions.", &[]),
    (crate::Architecture::Bpf, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[]),
    (crate::Architecture::Hexagon, "hvx", "Hexagon HVX instructions.", &[]),
    (crate::Architecture::Hexagon, "hvx-length128b", "Hexagon HVX 128B instructions.", &[Feature(80)]),
    (crate::Architecture::Hexagon, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[]),
    (crate::Architecture::Mips, "fp64", "Support 64-bit FP registers.", &[]),
    (crate::Architecture::Mips, "msa", "Mips MSA ASE.", &[Feature(83)]),
    (crate::Architecture::Mips, "virt", "Mips Virtualization ASE.", &[Feature(83)]),
    (crate::Architecture::Mips, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[Feature(83)]),
    (crate::Architecture::PowerPC, "altivec", "Enable Altivec instructions.", &[]),
    (crate::Architecture::PowerPC, "power10-vector", "Enable POWER10 vector instructions.", &[Feature(87), Feature(89), Feature(90), Feature(91), Feature(92), Feature(93)]),
    (crate::Architecture::PowerPC, "power8-altivec", "Enable POWER8 Altivec instructions.", &[Feature(87)]),
    (crate::Architecture::PowerPC, "power8-vector", "Enable POWER8 vector instructions.", &[Feature(87), Feature(89), Feature(93)]),
    (crate::Architecture::PowerPC, "power9-altivec", "Enable POWER9 Altivec instructions.", &[Feature(87), Feature(89)]),
    (crate::Architecture::PowerPC, "power9-vector", "Enable POWER9 vector instructions.", &[Feature(87), Feature(89), Feature(90), Feature(91), Feature(93)]),
    (crate::Architecture::PowerPC, "vsx", "Enable VSX instructions.", &[Feature(87)]),
    (crate::Architecture::PowerPC, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[]),
    (crate::Architecture::RiscV, "a", "'A' (Atomic Instructions).", &[]),
    (crate::Architecture::RiscV, "c", "'C' (Compressed Instructions).", &[]),
    (crate::Architecture::RiscV, "d", "'D' (Double-Precision Floating-Point).", &[Feature(99)]),
    (crate::Architecture::RiscV, "e", "Implements RV{32,64}E (provides 16 rather than 32 GPRs).", &[]),
    (crate::Architecture::RiscV, "f", "'F' (Single-Precision Floating-Point).", &[]),
    (crate::Architecture::RiscV, "fast-unaligned-access", "Has reasonably performant unaligned loads and stores (both scalar and vector).", &[]),
    (crate::Architecture::RiscV, "m", "'M' (Integer Multiplication and Division).", &[]),
    (crate::Architecture::RiscV, "relax", "Enable Linker relaxation..", &[]),
    (crate::Architecture::RiscV, "v", "'V' (Vector Extension for Application Processors).", &[Feature(97), Feature(99)]),
    (crate::Architecture::RiscV, "zba", "'Zba' (Address Generation Instructions).", &[]),
    (crate::Architecture::RiscV, "zbb", "'Zbb' (Basic Bit-Manipulation).", &[]),
    (crate::Architecture::RiscV, "zbc", "'Zbc' (Carry-Less Multiplication).", &[]),
    (crate::Architecture::RiscV, "zbkb", "'Zbkb' (Bitmanip instructions for Cryptography).", &[]),
    (crate::Architecture::RiscV, "zbkc", "'Zbkc' (Carry-less multiply instructions for Cryptography).", &[]),
    (crate::Architecture::RiscV, "zbkx", "'Zbkx' (Crossbar permutation instructions).", &[]),
    (crate::Architecture::RiscV, "zbs", "'Zbs' (Single-Bit Instructions).", &[]),
    (crate::Architecture::RiscV, "zdinx", "'Zdinx' (Double in Integer).", &[Feature(114)]),
    (crate::Architecture::RiscV, "zfh", "'Zfh' (Half-Precision Floating-Point).", &[Feature(99), Feature(113)]),
    (crate::Architecture::RiscV, "zfhmin", "'Zfhmin' (Half-Precision Floating-Point Minimal).", &[Feature(99)]),
    (crate::Architecture::RiscV, "zfinx", "'Zfinx' (Float in Integer).", &[]),
    (crate::Architecture::RiscV, "zhinx", "'Zhinx' (Half Float in Integer).", &[Feature(114), Feature(116)]),
    (crate::Architecture::RiscV, "zhinxmin", "'Zhinxmin' (Half Float in Integer Minimal).", &[Feature(114)]),
    (crate::Architecture::RiscV, "zk", "'Zk' (Standard scalar cryptography extension).", &[Feature(107), Feature(108), Feature(109), Feature(118), Feature(119), Feature(120), Feature(121), Feature(122), Feature(126)]),
    (crate::Architecture::RiscV, "zkn", "'Zkn' (NIST Algorithm Suite).", &[Feature(107), Feature(108), Feature(109), Feature(119), Feature(120), Feature(121)]),
    (crate::Architecture::RiscV, "zknd", "'Zknd' (NIST Suite: AES Decryption).", &[]),
    (crate::Architecture::RiscV, "zkne", "'Zkne' (NIST Suite: AES Encryption).", &[]),
    (crate::Architecture::RiscV, "zknh", "'Zknh' (NIST Suite: Hash Function Instructions).", &[]),
    (crate::Architecture::RiscV, "zkr", "'Zkr' (Entropy Source Extension).", &[]),
    (crate::Architecture::RiscV, "zks", "'Zks' (ShangMi Algorithm Suite).", &[Feature(107), Feature(108), Feature(109), Feature(124), Feature(125)]),
    (crate::Architecture::RiscV, "zksed", "'Zksed' (ShangMi Suite: SM4 Block Cipher Instructions).", &[]),
    (crate::Architecture::RiscV, "zksh", "'Zksh' (ShangMi Suite: SM3 Hash Function Instructions).", &[]),
    (crate::Architecture::RiscV, "zkt", "'Zkt' (Data Independent Execution Latency).", &[]),
    (crate::Architecture::RiscV, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[]),
    (crate::Architecture::Wasm, "atomics", "Enable Atomics.", &[]),
    (crate::Architecture::Wasm, "bulk-memory", "Enable bulk memory operations.", &[]),
    (crate::Architecture::Wasm, "exception-handling", "Enable Wasm exception handling.", &[]),
    (crate::Architecture::Wasm, "multivalue", "Enable multivalue blocks, instructions, and functions.", &[]),
    (crate::Architecture::Wasm, "mutable-globals", "Enable mutable globals.", &[]),
    (crate::Architecture::Wasm, "nontrapping-fptoint", "Enable non-trapping float-to-int conversion operators.", &[]),
    (crate::Architecture::Wasm, "reference-types", "Enable reference types.", &[]),
    (crate::Architecture::Wasm, "relaxed-simd", "Enable relaxed-simd instructions.", &[]),
    (crate::Architecture::Wasm, "sign-ext", "Enable sign extension operators.", &[]),
    (crate::Architecture::Wasm, "simd128", "Enable 128-bit SIMD.", &[]),
    (crate::Architecture::Wasm, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[]),
    (crate::Architecture::X86, "adx", "Support ADX instructions.", &[]),
    (crate::Architecture::X86, "aes", "Enable AES instructions.", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "avx", "Enable AVX instructions.", &[Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx2", "Enable AVX2 instructions.", &[Feature(141), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512bf16", "Support bfloat16 floating point.", &[Feature(141), Feature(142), Feature(145), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512bitalg", "Enable AVX-512 Bit Algorithms.", &[Feature(141), Feature(142), Feature(145), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512bw", "Enable AVX-512 Byte and Word Instructions.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512cd", "Enable AVX-512 Conflict Detection Instructions.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512dq", "Enable AVX-512 Doubleword and Quadword Instructions.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512er", "Enable AVX-512 Exponential and Reciprocal Instructions.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512f", "Enable AVX-512 instructions.", &[Feature(141), Feature(142), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512fp16", "Support 16-bit floating point.", &[Feature(141), Feature(142), Feature(145), Feature(147), Feature(149), Feature(155), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512ifma", "Enable AVX-512 Integer Fused Multiple-Add.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512pf", "Enable AVX-512 PreFetch Instructions.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512vbmi", "Enable AVX-512 Vector Byte Manipulation Instructions.", &[Feature(141), Feature(142), Feature(145), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512vbmi2", "Enable AVX-512 further Vector Byte Manipulation Instructions.", &[Feature(141), Feature(142), Feature(145), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512vl", "Enable AVX-512 Vector Length eXtensions.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512vnni", "Enable AVX-512 Vector Neural Network Instructions.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512vp2intersect", "Enable AVX-512 vp2intersect.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "avx512vpopcntdq", "Enable AVX-512 Population Count Instructions.", &[Feature(141), Feature(142), Feature(149), Feature(163), Feature(164), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "bmi1", "Support BMI instructions.", &[]),
    (crate::Architecture::X86, "bmi2", "Support BMI2 instructions.", &[]),
    (crate::Architecture::X86, "cmpxchg16b", "64-bit with cmpxchg16b (this is true for most x86-64 chips, but not the first AMD chips).", &[]),
    (crate::Architecture::X86, "ermsb", "REP MOVS/STOS are fast.", &[]),
    (crate::Architecture::X86, "f16c", "Support 16-bit floating point conversion instructions.", &[Feature(141), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "fma", "Enable three-operand fused multiple-add.", &[Feature(141), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "fxsr", "Support fxsave/fxrestore instructions.", &[]),
    (crate::Architecture::X86, "gfni", "Enable Galois Field Arithmetic Instructions.", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "lahfsahf", "Support LAHF and SAHF instructions in 64-bit mode.", &[]),
    (crate::Architecture::X86, "lzcnt", "Support LZCNT instruction.", &[]),
    (crate::Architecture::X86, "movbe", "Support MOVBE instruction.", &[]),
    (crate::Architecture::X86, "pclmulqdq", "Enable packed carry-less multiplication instructions.", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "popcnt", "Support POPCNT instruction.", &[]),
    (crate::Architecture::X86, "prfchw", "Support PRFCHW instructions.", &[]),
    (crate::Architecture::X86, "rdrand", "Support RDRAND instruction.", &[]),
    (crate::Architecture::X86, "rdseed", "Support RDSEED instruction.", &[]),
    (crate::Architecture::X86, "rtm", "Support RTM instructions.", &[]),
    (crate::Architecture::X86, "sha", "Enable SHA instructions.", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "sse", "Enable SSE instructions.", &[]),
    (crate::Architecture::X86, "sse2", "Enable SSE2 instructions.", &[Feature(177)]),
    (crate::Architecture::X86, "sse3", "Enable SSE3 instructions.", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "sse4.1", "Enable SSE 4.1 instructions.", &[Feature(177), Feature(178), Feature(179), Feature(183)]),
    (crate::Architecture::X86, "sse4.2", "Enable SSE 4.2 instructions.", &[Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "sse4a", "Support SSE 4a instructions.", &[Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "ssse3", "Enable SSSE3 instructions.", &[Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "tbm", "Enable TBM instructions.", &[]),
    (crate::Architecture::X86, "vaes", "Promote selected AES instructions to AVX512/AVX registers.", &[Feature(140), Feature(141), Feature(142), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "vpclmulqdq", "Enable vpclmulqdq instructions.", &[Feature(141), Feature(170), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "xsave", "Support xsave instructions.", &[]),
    (crate::Architecture::X86, "xsavec", "Support xsavec instructions.", &[Feature(187)]),
    (crate::Architecture::X86, "xsaveopt", "Support xsaveopt instructions.", &[Feature(187)]),
    (crate::Architecture::X86, "xsaves", "Support xsaves instructions.", &[Feature(187)]),
    (crate::Architecture::X86, "crt-static", "Enables C Run-time Libraries to be statically linked.", &[]),
];
const CPUS: &[(crate::Architecture, &str, &[Feature])] = &[
    (crate::Architecture::Arm, "arm1020e", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm1020t", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm1022e", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm10e", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm10tdmi", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm1136j-s", &[Feature(5), Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm1136jf-s", &[Feature(5), Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm1156t2-s", &[Feature(5), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(21)]),
    (crate::Architecture::Arm, "arm1156t2f-s", &[Feature(5), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(21)]),
    (crate::Architecture::Arm, "arm1176jz-s", &[Feature(14), Feature(15), Feature(16), Feature(17), Feature(21)]),
    (crate::Architecture::Arm, "arm1176jzf-s", &[Feature(14), Feature(15), Feature(16), Feature(17), Feature(21)]),
    (crate::Architecture::Arm, "arm710t", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm720t", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm7tdmi", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm7tdmi-s", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm8", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm810", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm9", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm920", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm920t", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm922t", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm926ej-s", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm940t", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm946e-s", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm966e-s", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm968e-s", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm9e", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "arm9tdmi", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "cortex-a12", &[Feature(0), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a15", &[Feature(0), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a17", &[Feature(0), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a32", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a35", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a5", &[Feature(0), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-a53", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a55", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a57", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a7", &[Feature(0), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a710", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a72", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a73", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a75", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a76", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a76ae", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a77", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a78", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a78c", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-a8", &[Feature(0), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-a9", &[Feature(0), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m0", &[Feature(8), Feature(12), Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m0plus", &[Feature(8), Feature(12), Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m1", &[Feature(8), Feature(12), Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m23", &[Feature(8), Feature(12), Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m3", &[Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m33", &[Feature(5), Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m35p", &[Feature(5), Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m4", &[Feature(5), Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m52", &[Feature(5), Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m55", &[Feature(5), Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m7", &[Feature(5), Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-m85", &[Feature(5), Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-r4", &[Feature(5), Feature(10), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-r4f", &[Feature(5), Feature(10), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-r5", &[Feature(5), Feature(10), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-r52", &[Feature(2), Feature(5), Feature(10), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-r7", &[Feature(5), Feature(10), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-r8", &[Feature(5), Feature(10), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "cortex-x1", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cortex-x1c", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "cyclone", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "ep9312", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "exynos-m3", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "exynos-m4", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "exynos-m5", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "generic", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "iwmmxt", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "krait", &[Feature(0), Feature(5), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "kryo", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "mpcore", &[Feature(15), Feature(16), Feature(17), Feature(21)]),
    (crate::Architecture::Arm, "mpcorenovfp", &[Feature(15), Feature(16), Feature(17), Feature(21)]),
    (crate::Architecture::Arm, "neoverse-n1", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "neoverse-n2", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "neoverse-v1", &[Feature(0), Feature(2), Feature(5), Feature(13), Feature(14), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(20), Feature(21), Feature(24)]),
    (crate::Architecture::Arm, "sc000", &[Feature(8), Feature(12), Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "sc300", &[Feature(8), Feature(12), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "strongarm", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "strongarm110", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "strongarm1100", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "strongarm1110", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::Arm, "swift", &[Feature(0), Feature(5), Feature(13), Feature(15), Feature(16), Feature(17), Feature(18), Feature(19), Feature(21)]),
    (crate::Architecture::Arm, "xscale", &[Feature(15), Feature(16), Feature(21)]),
    (crate::Architecture::AArch64, "a64fx", &[Feature(29), Feature(32), Feature(36), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(55), Feature(57), Feature(62), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "ampere1", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(38), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(51), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "ampere1a", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(38), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(51), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(59), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "ampere1b", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(51), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(59), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(75), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a10", &[Feature(26), Feature(29), Feature(43), Feature(46), Feature(49), Feature(50), Feature(55), Feature(57), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a11", &[Feature(26), Feature(29), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(55), Feature(57), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a12", &[Feature(26), Feature(29), Feature(32), Feature(36), Feature(39), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(69), Feature(70), Feature(71), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a13", &[Feature(26), Feature(29), Feature(30), Feature(31), Feature(32), Feature(36), Feature(37), Feature(38), Feature(39), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(57), Feature(58), Feature(69), Feature(70), Feature(71), Feature(72), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a14", &[Feature(26), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a15", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a16", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a17", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "apple-a7", &[Feature(26), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "apple-a8", &[Feature(26), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "apple-a9", &[Feature(26), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "apple-latest", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "apple-m1", &[Feature(26), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(76)]),
    (crate::Architecture::AArch64, "apple-m2", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "apple-m3", &[Feature(26), Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(57), Feature(58), Feature(61), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(76)]),
    (crate::Architecture::AArch64, "apple-s4", &[Feature(26), Feature(29), Feature(32), Feature(36), Feature(39), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(69), Feature(70), Feature(71), Feature(76)]),
    (crate::Architecture::AArch64, "apple-s5", &[Feature(26), Feature(29), Feature(32), Feature(36), Feature(39), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(69), Feature(70), Feature(71), Feature(76)]),
    (crate::Architecture::AArch64, "carmel", &[Feature(26), Feature(29), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(52), Feature(55), Feature(57), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a34", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "cortex-a35", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "cortex-a510", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a520", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(75), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a53", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "cortex-a55", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a57", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "cortex-a65", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a65ae", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a710", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a715", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(60), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a72", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "cortex-a720", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(60), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(75), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a73", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "cortex-a75", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a76", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a76ae", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a77", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a78", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(60), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-a78c", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(38), Feature(39), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(60), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-r82", &[Feature(29), Feature(30), Feature(31), Feature(32), Feature(36), Feature(37), Feature(38), Feature(39), Feature(42), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61)]),
    (crate::Architecture::AArch64, "cortex-x1", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(60), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-x1c", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(38), Feature(39), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(57), Feature(60), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-x2", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-x3", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(60), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(76)]),
    (crate::Architecture::AArch64, "cortex-x4", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(60), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(74), Feature(75), Feature(76)]),
    (crate::Architecture::AArch64, "cyclone", &[Feature(26), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "exynos-m3", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "exynos-m4", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(55), Feature(57), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "exynos-m5", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(55), Feature(57), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "falkor", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(55), Feature(57)]),
    (crate::Architecture::AArch64, "generic", &[Feature(46)]),
    (crate::Architecture::AArch64, "kryo", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "neoverse-512tvb", &[Feature(26), Feature(27), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(51), Feature(52), Feature(53), Feature(54), Feature(55), Feature(57), Feature(60), Feature(61), Feature(62), Feature(69), Feature(70), Feature(71), Feature(72), Feature(76)]),
    (crate::Architecture::AArch64, "neoverse-e1", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "neoverse-n1", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(39), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(60), Feature(61), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::AArch64, "neoverse-n2", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(76)]),
    (crate::Architecture::AArch64, "neoverse-v1", &[Feature(26), Feature(27), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(41), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(51), Feature(52), Feature(53), Feature(54), Feature(55), Feature(57), Feature(60), Feature(61), Feature(62), Feature(69), Feature(70), Feature(71), Feature(72), Feature(76)]),
    (crate::Architecture::AArch64, "neoverse-v2", &[Feature(27), Feature(28), Feature(29), Feature(30), Feature(31), Feature(32), Feature(33), Feature(36), Feature(37), Feature(38), Feature(39), Feature(40), Feature(41), Feature(42), Feature(43), Feature(44), Feature(45), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(51), Feature(52), Feature(53), Feature(54), Feature(55), Feature(56), Feature(60), Feature(61), Feature(62), Feature(63), Feature(65), Feature(69), Feature(70), Feature(71), Feature(72), Feature(73), Feature(76)]),
    (crate::Architecture::AArch64, "saphira", &[Feature(26), Feature(29), Feature(30), Feature(31), Feature(32), Feature(36), Feature(38), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(54), Feature(55), Feature(57), Feature(60), Feature(69), Feature(70), Feature(71), Feature(72), Feature(76)]),
    (crate::Architecture::AArch64, "thunderx", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "thunderx2t99", &[Feature(26), Feature(29), Feature(43), Feature(44), Feature(46), Feature(49), Feature(55), Feature(57), Feature(69), Feature(76)]),
    (crate::Architecture::AArch64, "thunderx3t110", &[Feature(26), Feature(29), Feature(32), Feature(36), Feature(42), Feature(43), Feature(44), Feature(46), Feature(47), Feature(48), Feature(49), Feature(50), Feature(52), Feature(53), Feature(55), Feature(57), Feature(69), Feature(70), Feature(71), Feature(76)]),
    (crate::Architecture::AArch64, "thunderxt81", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "thunderxt83", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "thunderxt88", &[Feature(26), Feature(29), Feature(46), Feature(50), Feature(57)]),
    (crate::Architecture::AArch64, "tsv110", &[Feature(26), Feature(29), Feature(31), Feature(32), Feature(36), Feature(37), Feature(39), Feature(42), Feature(43), Feature(44), Feature(46), Feature(49), Feature(50), Feature(52), Feature(55), Feature(57), Feature(60), Feature(69), Feature(70), Feature(76)]),
    (crate::Architecture::Bpf, "generic", &[]),
    (crate::Architecture::Bpf, "probe", &[]),
    (crate::Architecture::Bpf, "v1", &[]),
    (crate::Architecture::Bpf, "v2", &[]),
    (crate::Architecture::Bpf, "v3", &[Feature(78)]),
    (crate::Architecture::Bpf, "v4", &[Feature(78)]),
    (crate::Architecture::Hexagon, "generic", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv5", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv55", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv60", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv62", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv65", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv66", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv67", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv67t", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv68", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv69", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv71", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv71t", &[Feature(80), Feature(81)]),
    (crate::Architecture::Hexagon, "hexagonv73", &[Feature(80), Feature(81)]),
    (crate::Architecture::Mips, "generic", &[Feature(83)]),
    (crate::Architecture::Mips, "mips1", &[Feature(83)]),
    (crate::Architecture::Mips, "mips2", &[Feature(83)]),
    (crate::Architecture::Mips, "mips3", &[Feature(83)]),
    (crate::Architecture::Mips, "mips32", &[Feature(83)]),
    (crate::Architecture::Mips, "mips32r2", &[Feature(83)]),
    (crate::Architecture::Mips, "mips32r3", &[Feature(83)]),
    (crate::Architecture::Mips, "mips32r5", &[Feature(83)]),
    (crate::Architecture::Mips, "mips32r6", &[Feature(83)]),
    (crate::Architecture::Mips, "mips4", &[Feature(83)]),
    (crate::Architecture::Mips, "mips5", &[Feature(83)]),
    (crate::Architecture::Mips, "mips64", &[Feature(83)]),
    (crate::Architecture::Mips, "mips64r2", &[Feature(83)]),
    (crate::Architecture::Mips, "mips64r3", &[Feature(83)]),
    (crate::Architecture::Mips, "mips64r5", &[Feature(83)]),
    (crate::Architecture::Mips, "mips64r6", &[Feature(83)]),
    (crate::Architecture::Mips, "octeon", &[Feature(83)]),
    (crate::Architecture::Mips, "octeon+", &[Feature(83)]),
    (crate::Architecture::Mips, "p5600", &[Feature(83)]),
    (crate::Architecture::PowerPC, "440", &[]),
    (crate::Architecture::PowerPC, "450", &[]),
    (crate::Architecture::PowerPC, "601", &[]),
    (crate::Architecture::PowerPC, "602", &[]),
    (crate::Architecture::PowerPC, "603", &[]),
    (crate::Architecture::PowerPC, "603e", &[]),
    (crate::Architecture::PowerPC, "603ev", &[]),
    (crate::Architecture::PowerPC, "604", &[]),
    (crate::Architecture::PowerPC, "604e", &[]),
    (crate::Architecture::PowerPC, "620", &[]),
    (crate::Architecture::PowerPC, "7400", &[Feature(87)]),
    (crate::Architecture::PowerPC, "7450", &[Feature(87)]),
    (crate::Architecture::PowerPC, "750", &[]),
    (crate::Architecture::PowerPC, "970", &[Feature(87)]),
    (crate::Architecture::PowerPC, "a2", &[]),
    (crate::Architecture::PowerPC, "e500", &[]),
    (crate::Architecture::PowerPC, "e500mc", &[]),
    (crate::Architecture::PowerPC, "e5500", &[]),
    (crate::Architecture::PowerPC, "future", &[Feature(87), Feature(88), Feature(89), Feature(90), Feature(91), Feature(92), Feature(93)]),
    (crate::Architecture::PowerPC, "g3", &[]),
    (crate::Architecture::PowerPC, "g4", &[Feature(87)]),
    (crate::Architecture::PowerPC, "g4+", &[Feature(87)]),
    (crate::Architecture::PowerPC, "g5", &[Feature(87)]),
    (crate::Architecture::PowerPC, "generic", &[]),
    (crate::Architecture::PowerPC, "ppc", &[]),
    (crate::Architecture::PowerPC, "ppc32", &[]),
    (crate::Architecture::PowerPC, "ppc64", &[Feature(87)]),
    (crate::Architecture::PowerPC, "ppc64le", &[Feature(87), Feature(89), Feature(90), Feature(93)]),
    (crate::Architecture::PowerPC, "pwr10", &[Feature(87), Feature(88), Feature(89), Feature(90), Feature(91), Feature(92), Feature(93)]),
    (crate::Architecture::PowerPC, "pwr3", &[Feature(87)]),
    (crate::Architecture::PowerPC, "pwr4", &[Feature(87)]),
    (crate::Architecture::PowerPC, "pwr5", &[Feature(87)]),
    (crate::Architecture::PowerPC, "pwr5x", &[Feature(87)]),
    (crate::Architecture::PowerPC, "pwr6", &[Feature(87)]),
    (crate::Architecture::PowerPC, "pwr6x", &[Feature(87)]),
    (crate::Architecture::PowerPC, "pwr7", &[Feature(87), Feature(93)]),
    (crate::Architecture::PowerPC, "pwr8", &[Feature(87), Feature(89), Feature(90), Feature(93)]),
    (crate::Architecture::PowerPC, "pwr9", &[Feature(87), Feature(89), Feature(90), Feature(91), Feature(92), Feature(93)]),
    (crate::Architecture::RiscV, "generic", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "generic-rv32", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "generic-rv64", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "rocket", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "rocket-rv32", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "rocket-rv64", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-7-series", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-e20", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-e21", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-e24", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-e31", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-e34", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-e76", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-p450", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(100), Feature(101), Feature(104), Feature(105), Feature(110), Feature(113)]),
    (crate::Architecture::RiscV, "sifive-p670", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(100), Feature(101), Feature(103), Feature(104), Feature(105), Feature(110), Feature(113)]),
    (crate::Architecture::RiscV, "sifive-s21", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-s51", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-s54", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-s76", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-u54", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-u74", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "sifive-x280", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101), Feature(103), Feature(104), Feature(105), Feature(112), Feature(113)]),
    (crate::Architecture::RiscV, "syntacore-scr1-base", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "syntacore-scr1-max", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101)]),
    (crate::Architecture::RiscV, "veyron-v1", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101), Feature(104), Feature(105), Feature(106), Feature(110)]),
    (crate::Architecture::RiscV, "xiangshan-nanhu", &[Feature(95), Feature(96), Feature(97), Feature(99), Feature(101), Feature(104), Feature(105), Feature(106), Feature(107), Feature(108), Feature(109), Feature(110), Feature(118), Feature(119), Feature(120), Feature(121), Feature(124), Feature(125)]),
    (crate::Architecture::Wasm, "bleeding-edge", &[Feature(128), Feature(129), Feature(132), Feature(133), Feature(136), Feature(137)]),
    (crate::Architecture::Wasm, "generic", &[Feature(132), Feature(136)]),
    (crate::Architecture::Wasm, "mvp", &[]),
    (crate::Architecture::X86, "alderlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "amdfam10", &[Feature(161), Feature(165), Feature(167), Feature(168), Feature(171), Feature(172), Feature(177), Feature(178), Feature(179), Feature(182)]),
    (crate::Architecture::X86, "arrowlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "arrowlake-s", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "arrowlake_s", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "athlon", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "athlon-4", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "athlon-fx", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "athlon-mp", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "athlon-tbird", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "athlon-xp", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "athlon64", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "athlon64-sse3", &[Feature(161), Feature(165), Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "atom", &[Feature(161), Feature(165), Feature(167), Feature(169), Feature(177), Feature(178), Feature(179), Feature(183)]),
    (crate::Architecture::X86, "atom_sse4_2", &[Feature(161), Feature(165), Feature(167), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "atom_sse4_2_movbe", &[Feature(140), Feature(161), Feature(165), Feature(167), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "barcelona", &[Feature(161), Feature(165), Feature(167), Feature(168), Feature(171), Feature(172), Feature(177), Feature(178), Feature(179), Feature(182)]),
    (crate::Architecture::X86, "bdver1", &[Feature(140), Feature(141), Feature(161), Feature(165), Feature(167), Feature(168), Feature(170), Feature(171), Feature(172), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(187)]),
    (crate::Architecture::X86, "bdver2", &[Feature(140), Feature(141), Feature(159), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(170), Feature(171), Feature(172), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(184), Feature(187)]),
    (crate::Architecture::X86, "bdver3", &[Feature(140), Feature(141), Feature(159), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(170), Feature(171), Feature(172), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(184), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "bdver4", &[Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(184), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "bonnell", &[Feature(161), Feature(165), Feature(167), Feature(169), Feature(177), Feature(178), Feature(179), Feature(183)]),
    (crate::Architecture::X86, "broadwell", &[Feature(139), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "btver1", &[Feature(161), Feature(165), Feature(167), Feature(168), Feature(171), Feature(172), Feature(177), Feature(178), Feature(179), Feature(182), Feature(183)]),
    (crate::Architecture::X86, "btver2", &[Feature(140), Feature(141), Feature(159), Feature(161), Feature(163), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "c3", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "c3-2", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "cannonlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(145), Feature(146), Feature(147), Feature(149), Feature(151), Feature(153), Feature(155), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "cascadelake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(145), Feature(146), Feature(147), Feature(149), Feature(155), Feature(156), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "clearwaterforest", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "cooperlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(143), Feature(145), Feature(146), Feature(147), Feature(149), Feature(155), Feature(156), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "core-avx-i", &[Feature(141), Feature(161), Feature(163), Feature(165), Feature(167), Feature(170), Feature(171), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "core-avx2", &[Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "core2", &[Feature(161), Feature(165), Feature(167), Feature(177), Feature(178), Feature(179), Feature(183)]),
    (crate::Architecture::X86, "core_2_duo_sse4_1", &[Feature(161), Feature(165), Feature(167), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "core_2_duo_ssse3", &[Feature(161), Feature(165), Feature(167), Feature(177), Feature(178), Feature(179), Feature(183)]),
    (crate::Architecture::X86, "core_2nd_gen_avx", &[Feature(141), Feature(161), Feature(165), Feature(167), Feature(170), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "core_3rd_gen_avx", &[Feature(141), Feature(161), Feature(163), Feature(165), Feature(167), Feature(170), Feature(171), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "core_4th_gen_avx", &[Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "core_4th_gen_avx_tsx", &[Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "core_5th_gen_avx", &[Feature(139), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "core_5th_gen_avx_tsx", &[Feature(139), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "core_aes_pclmulqdq", &[Feature(161), Feature(165), Feature(167), Feature(170), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "core_i7_sse4_2", &[Feature(161), Feature(165), Feature(167), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "corei7", &[Feature(161), Feature(165), Feature(167), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "corei7-avx", &[Feature(141), Feature(161), Feature(165), Feature(167), Feature(170), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "emeraldrapids", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(143), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(150), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "generic", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "geode", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "goldmont", &[Feature(140), Feature(161), Feature(165), Feature(167), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "goldmont-plus", &[Feature(140), Feature(161), Feature(165), Feature(167), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "goldmont_plus", &[Feature(140), Feature(161), Feature(165), Feature(167), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "gracemont", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "grandridge", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "graniterapids", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(143), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(150), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "graniterapids-d", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(143), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(150), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "graniterapids_d", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(143), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(150), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "haswell", &[Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "i386", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "i486", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "i586", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "i686", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "icelake-client", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "icelake-server", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "icelake_client", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "icelake_server", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "ivybridge", &[Feature(141), Feature(161), Feature(163), Feature(165), Feature(167), Feature(170), Feature(171), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "k6", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "k6-2", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "k6-3", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "k8", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "k8-sse3", &[Feature(161), Feature(165), Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "knl", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(146), Feature(148), Feature(149), Feature(152), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "knm", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(146), Feature(148), Feature(149), Feature(152), Feature(158), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "lakemont", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "lunarlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "meteorlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "mic_avx512", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(146), Feature(148), Feature(149), Feature(152), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "nehalem", &[Feature(161), Feature(165), Feature(167), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "nocona", &[Feature(161), Feature(165), Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "opteron", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "opteron-sse3", &[Feature(161), Feature(165), Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "pantherlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "penryn", &[Feature(161), Feature(165), Feature(167), Feature(177), Feature(178), Feature(179), Feature(180), Feature(183)]),
    (crate::Architecture::X86, "pentium", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium-m", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium-mmx", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium2", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium3", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium3m", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium4", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium4m", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium_4", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium_4_sse3", &[Feature(165), Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "pentium_ii", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium_iii", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium_iii_no_xmm_regs", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium_m", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium_mmx", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentium_pro", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "pentiumpro", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "prescott", &[Feature(165), Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "raptorlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "rocketlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "sandybridge", &[Feature(141), Feature(161), Feature(165), Feature(167), Feature(170), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(189)]),
    (crate::Architecture::X86, "sapphirerapids", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(143), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(150), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "sierraforest", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "silvermont", &[Feature(161), Feature(165), Feature(167), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "skx", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(145), Feature(146), Feature(147), Feature(149), Feature(155), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "skylake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "skylake-avx512", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(145), Feature(146), Feature(147), Feature(149), Feature(155), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "skylake_avx512", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(145), Feature(146), Feature(147), Feature(149), Feature(155), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "slm", &[Feature(161), Feature(165), Feature(167), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "tigerlake", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(157), Feature(158), Feature(159), Feature(160), Feature(161), Feature(162), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "tremont", &[Feature(140), Feature(161), Feature(165), Feature(166), Feature(167), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "westmere", &[Feature(161), Feature(165), Feature(167), Feature(170), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "winchip-c6", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "winchip2", &[Feature(177), Feature(178)]),
    (crate::Architecture::X86, "x86-64", &[Feature(165), Feature(177), Feature(178)]),
    (crate::Architecture::X86, "x86-64-v2", &[Feature(161), Feature(165), Feature(167), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183)]),
    (crate::Architecture::X86, "x86-64-v3", &[Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187)]),
    (crate::Architecture::X86, "x86-64-v4", &[Feature(141), Feature(142), Feature(145), Feature(146), Feature(147), Feature(149), Feature(155), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(171), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(183), Feature(187)]),
    (crate::Architecture::X86, "yonah", &[Feature(165), Feature(177), Feature(178), Feature(179)]),
    (crate::Architecture::X86, "znver1", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "znver2", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "znver3", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
    (crate::Architecture::X86, "znver4", &[Feature(139), Feature(140), Feature(141), Feature(142), Feature(143), Feature(144), Feature(145), Feature(146), Feature(147), Feature(149), Feature(151), Feature(153), Feature(154), Feature(155), Feature(156), Feature(158), Feature(159), Feature(160), Feature(161), Feature(163), Feature(164), Feature(165), Feature(166), Feature(167), Feature(168), Feature(169), Feature(170), Feature(171), Feature(172), Feature(173), Feature(174), Feature(176), Feature(177), Feature(178), Feature(179), Feature(180), Feature(181), Feature(182), Feature(183), Feature(185), Feature(186), Feature(187), Feature(188), Feature(189), Feature(190)]),
];
/// The target of the current build.
#[allow(clippy::let_and_return)]
pub const CURRENT_TARGET: Target = {
    let arch = Architecture::X86;
    let target = Target::new(arch);
    let target = if let Ok(feature) = Feature::new(arch, "fxsr") { target.with_feature(feature) } else { target };
    let target = if let Ok(feature) = Feature::new(arch, "sse") { target.with_feature(feature) } else { target };
    let target = if let Ok(feature) = Feature::new(arch, "sse2") { target.with_feature(feature) } else { target };
    target
};
