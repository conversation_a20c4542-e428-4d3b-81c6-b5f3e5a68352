/home/<USER>/repos/data_auditing/rust_data_processor/target/debug/build/portable-atomic-f3e2199342e9a17f/build_script_build-f3e2199342e9a17f.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/build.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/version.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/src/gen/build.rs

/home/<USER>/repos/data_auditing/rust_data_processor/target/debug/build/portable-atomic-f3e2199342e9a17f/build_script_build-f3e2199342e9a17f: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/build.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/version.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/src/gen/build.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/build.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/version.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/portable-atomic-1.11.1/src/gen/build.rs:

# env-dep:CARGO_PKG_NAME=portable-atomic
