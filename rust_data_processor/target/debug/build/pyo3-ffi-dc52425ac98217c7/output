cargo:rerun-if-env-changed=PYO3_CROSS
cargo:rerun-if-env-changed=PYO3_CROSS_LIB_DIR
cargo:rerun-if-env-changed=PYO3_CROSS_PYTHON_VERSION
cargo:rerun-if-env-changed=PYO3_CROSS_PYTHON_IMPLEMENTATION
cargo:rerun-if-env-changed=PYO3_PRINT_CONFIG
cargo:PYO3_CONFIG=696d706c656d656e746174696f6e3d43507974686f6e0a76657273696f6e3d332e31320a7368617265643d747275650a616269333d66616c73650a6c69625f6e616d653d707974686f6e332e31320a6c69625f6469723d2f686f6d652f6d667461646d696e2f6d696e69636f6e6461332f6c69620a65786563757461626c653d2f686f6d652f6d667461646d696e2f6d696e69636f6e6461332f62696e2f707974686f6e0a706f696e7465725f77696474683d36340a6275696c645f666c6167733d0a73757070726573735f6275696c645f7363726970745f6c696e6b5f6c696e65733d66616c73650a
cargo:rustc-cfg=Py_3_6
cargo:rustc-cfg=Py_3_7
cargo:rustc-cfg=Py_3_8
cargo:rustc-cfg=Py_3_9
cargo:rustc-cfg=Py_3_10
cargo:rustc-cfg=Py_3_11
cargo:rustc-cfg=Py_3_12
cargo:rustc-cfg=thread_local_const_init
cargo:rustc-cfg=invalid_from_utf8_lint
