cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=configure.rs
cargo:rustc-check-cfg=cfg(assert_no_panic)
cargo:rustc-check-cfg=cfg(intrinsics_enabled)
cargo:rustc-check-cfg=cfg(arch_enabled)
cargo:rustc-cfg=arch_enabled
cargo:rustc-check-cfg=cfg(optimizations_enabled)
cargo:rustc-check-cfg=cfg(x86_no_sse)
cargo:rustc-env=CFG_CARGO_FEATURES=["arch", "default"]
cargo:rustc-env=CFG_OPT_LEVEL=0
cargo:rustc-env=CFG_TARGET_FEATURES=["fxsr", "sse", "sse2"]
cargo:rustc-check-cfg=cfg(f16_enabled)
cargo:rustc-check-cfg=cfg(f128_enabled)
