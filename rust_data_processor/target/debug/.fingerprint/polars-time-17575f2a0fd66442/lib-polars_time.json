{"rustc": 11410426090777951712, "features": "[\"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\"]", "declared_features": "[\"chrono-tz\", \"default\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"fmt\", \"rolling_window\", \"serde\", \"test\", \"timezones\"]", "target": 13497526869155830988, "profile": 2241668132362809309, "path": 9843719514495126963, "deps": [[1458751047317679729, "polars_core", false, 12785791926672904966], [2752773142256470517, "arrow", false, 1554403704926783477], [3419428956812390430, "smartstring", false, 9692665678905392005], [3722963349756955755, "once_cell", false, 6293031810255931828], [6643084693527522471, "polars_utils", false, 9121758026600700075], [9101573641635189135, "polars_ops", false, 10848656423973692580], [9451456094439810778, "regex", false, 16501748659867266115], [9897246384292347999, "chrono", false, 5502450951822104167], [11610044098279991895, "now", false, 15264725525911977798], [11967285383858598289, "polars_error", false, 15057136308127966918], [17106256174509013259, "atoi", false, 3098788394997104174]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-time-17575f2a0fd66442/dep-lib-polars_time", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}